<template>
    <div class="p-6">
        <!-- Media Generation Header -->
        <div class="flex items-center justify-between mb-6" v-if="mediaItem.status !== 'completed'">
            <div class="flex items-center gap-3">
                <div class="w-12 h-12 rounded-full flex items-center justify-center"
                     :class="getStatusColor(mediaItem.status)">
                    <VsxIcon :iconName="getStatusIcon(mediaItem.status)" :size="24" type="linear" />
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900">{{ getStatusText(mediaItem.status) }}</p>
                    <p class="text-xs text-gray-500">{{ mediaItem.type === 'image' ? 'Image' : 'Video' }} Generation</p>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center gap-2">
                <button
                    v-if="mediaItem.status === 'pending' || mediaItem.status === 'processing'"
                    @click="$emit('refresh', mediaItem.taskId)"
                    class="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-lg hover:bg-gray-50"
                    title="Refresh status"
                >
                    <VsxIcon iconName="Refresh" :size="16"  type="linear" />
                </button>
            </div>
        </div>

        <!-- Enhanced Loading State with Sophisticated Skeleton -->
        <div v-if="mediaItem.status === 'pending' || mediaItem.status === 'processing'" class="space-y-6">
            <!-- Progress Bar -->
            <div class="flex items-center gap-4">
                <div class="flex-1 bg-gray-100 rounded-full h-2">
                    <div
                        class="h-2 rounded-full transition-all duration-500 ease-out"
                        :class="getProgressBarColor(mediaItem.status)"
                        :style="{ width: getProgressWidth(mediaItem.status) }"
                    ></div>
                </div>
                <span class="text-xs text-gray-500 min-w-[60px] font-medium">
                    {{ getProgressPercentage(mediaItem.status) }}%
                </span>
            </div>

            <!-- Modern Grid Skeleton Loading for Images -->
            <div v-if="mediaItem.type === 'image'" class="image-gallery-grid">
                <div
                    v-for="n in getExpectedImageCount()"
                    :key="`skeleton-${n}`"
                    class="skeleton-tile"
                >
                    <div class="skeleton-content">
                        <div class="skeleton-spinner"></div>
                    </div>
                </div>
            </div>

            <!-- Sophisticated Skeleton Loading for Videos -->
            <div v-else-if="mediaItem.type === 'video'" class="space-y-4">
                <div
                    v-for="n in getExpectedVideoCount()"
                    :key="`video-skeleton-${n}`"
                    class="relative w-full aspect-video bg-gradient-to-r from-gray-100 via-gray-50 to-gray-100 rounded-xl animate-shimmer"
                    style="background-size: 200% 100%;"
                >
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                            <div class="w-6 h-6 border-l-4 border-white/40 rounded-full animate-spin"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Generated Media Display -->
        <div v-if="mediaItem.status === 'completed' && mediaItem.result">
            <!-- Image Display -->
            <div v-if="mediaItem.type === 'image'">
                <!-- Image Count Display -->
                <div v-if="processedImages.length > 0" class="mb-6">
                    <span class="text-sm font-medium text-gray-700">
                        {{ processedImages.length }} image{{ processedImages.length > 1 ? 's' : '' }}
                        {{ mediaItem.isUploaded ? 'uploaded' : 'generated' }}
                    </span>
                </div>

                <!-- Modern Clean Grid Layout -->
                <div v-if="processedImages.length > 0" class="image-gallery-grid">
                    <div
                        v-for="(image, index) in processedImages"
                        :key="`grid-${index}`"
                        class="image-tile"
                        @click="handleImageClick(index)"
                    >
                        <img
                            :src="image.url"
                            :alt="image.alt"
                            class="image-tile-img"
                            @load="handleImageLoad"
                            @error="handleImageError"
                        />
                        <!-- Loading indicator for each image -->
                        <div v-if="!imageLoadStates[index]" class="absolute inset-0 bg-gray-100 flex items-center justify-center">
                            <div class="skeleton-spinner"></div>
                        </div>

                        <!-- Upload Badge for uploaded media -->
                        <div v-if="mediaItem.isUploaded" class="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full p-1.5 shadow-sm">
                            <VsxIcon iconName="DocumentUpload" :size="12" type="linear" class="text-gray-600" />
                        </div>

                        <div class="image-tile-overlay"></div>
                    </div>
                </div>

                <!-- No Images Found Message -->
                <div v-else class="text-center py-8 text-gray-500">
                    <VsxIcon iconName="Gallery" :size="48" class="mx-auto mb-4 text-gray-300" />
                    <p>No images found in the generation result</p>
                </div>
            </div>

            <!-- Enhanced Video Display -->
            <div v-else-if="mediaItem.type === 'video'" class="space-y-6">
                <div v-for="(video, index) in getVideoResults(mediaItem.result)" :key="index" class="relative">
                    <video
                        :src="video.url"
                        controls
                        class="w-full aspect-video rounded-xl shadow-sm"
                        @loadeddata="$emit('media-loaded')"
                        @error="handleVideoError"
                    >
                        Your browser does not support the video tag.
                    </video>

                    <!-- Upload Badge for uploaded video -->
                    <div v-if="mediaItem.isUploaded" class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-sm">
                        <VsxIcon iconName="DocumentUpload" :size="14" type="linear" class="text-gray-600" />
                    </div>
                    <div class="flex items-center justify-between mt-3 px-1">
                        <span class="text-sm text-gray-600">
                            Duration: {{ video.duration }}s
                        </span>
                        <a
                            :href="video.url"
                            target="_blank"
                            class="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
                        >
                            Download
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Error Display with Retry Functionality -->
        <div v-else-if="mediaItem.status === 'failed'" class="space-y-4">
            <div class="p-6 bg-red-50 border border-red-100 rounded-xl">
                <div class="flex items-start gap-4">
                    <div class="flex-shrink-0">
                        <VsxIcon iconName="CloseCircle" :size="32" type="linear" class="text-red-500" />
                    </div>
                    <div class="flex-1">
                        <h3 class="text-sm font-semibold text-red-800 mb-1">Generation Failed</h3>
                        <p class="text-sm text-red-700 mb-4">
                            {{ getErrorMessage(mediaItem.result?.error) }}
                        </p>
                        <div class="flex items-center gap-3">
                            <button
                                @click="handleRetry"
                                :disabled="isRetrying"
                                class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <span v-if="isRetrying" class="flex items-center gap-2">
                                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                    Retrying...
                                </span>
                                <span v-else>Try Again</span>
                            </button>
                            <button
                                @click="$emit('cancel', mediaItem.taskId)"
                                class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Image Modal -->
        <ImageModal
            :is-open="imageModal.isOpen.value"
            :images="imageModal.images.value"
            :current-index="imageModal.currentIndex.value"
            :current-image="imageModal.currentImage.value"
            @close="imageModal.closeModal"
            @navigate="imageModal.navigateImage"
            @image-load="handleModalImageLoad"
        >
        </ImageModal>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { VsxIcon } from 'vue-iconsax'
import ImageModal from '@/Components/Common/ImageModal.vue'
import { useImageModal } from '@/composables/useImageModal.js'

const props = defineProps({
    mediaItem: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['refresh', 'cancel', 'media-loaded', 'retry'])

// Reactive state
const isRetrying = ref(false)
const imageLoadStates = ref({})

// Use the image modal composable
const imageModal = useImageModal()

// Computed property to process images from API response
const processedImages = computed(() => {
    if (!props.mediaItem.result) {
        return []
    }

    const images = []
    const result = props.mediaItem.result

    // Handle expected API response structure: result.output.image_url + result.output.temporary_image_urls
    if (result.output) {
        const output = result.output
        console.log('GeneratedMedia: Found output object:', output)

        // Add primary image_url first if it exists
        if (output.image_url) {
            images.push({
                url: output.image_url,
                alt: 'Primary generated image',
                width: '1024',
                height: '1024',
                isPrimary: true
            })
        }

        // Add all temporary image variations
        if (output.temporary_image_urls && Array.isArray(output.temporary_image_urls)) {
            output.temporary_image_urls.forEach((url, index) => {
                if (url) {
                    images.push({
                        url: url,
                        alt: `Generated variation ${index + 1}`,
                        width: '1024',
                        height: '1024',
                        isPrimary: false
                    })
                }
            })
        }

        // Add any additional image_urls if present
        if (output.image_urls && Array.isArray(output.image_urls)) {
            output.image_urls.forEach((url, index) => {
                if (url) {
                    // Check if this URL is already in our images array to avoid duplicates
                    const isDuplicate = images.some(img => img.url === url)
                    if (!isDuplicate) {
                        images.push({
                            url: url,
                            alt: `Generated image ${index + 1}`,
                            width: '1024',
                            height: '1024',
                            isPrimary: false
                        })
                    }
                }
            })
        }
    }

    // Fallback to current structure: result.images array (backward compatibility)
    if (images.length === 0 && result.images && Array.isArray(result.images)) {
        return result.images.map((imageItem, index) => ({
            url: imageItem.url || imageItem,
            alt: `Generated image ${index + 1}`,
            width: '1024',
            height: '1024',
            isPrimary: index === 0
        }))
    }

    // Additional fallback for direct array structure
    if (images.length === 0 && Array.isArray(result)) {
        return result.map((item, index) => ({
            url: item.url || item,
            alt: `Generated image ${index + 1}`,
            width: item.width || '1024',
            height: item.height || '1024',
            isPrimary: index === 0
        }))
    }

    // Final fallback for single image structure
    if (images.length === 0 && result.url) {
        return [{
            url: result.url,
            alt: 'Generated image',
            width: result.width || '1024',
            height: result.height || '1024',
            isPrimary: true
        }]
    }

    return images
})

const handleImageClick = (index) => {
    imageModal.openModal(processedImages.value, index)
}

const handleImageLoad = (event) => {
    // Find the index of the loaded image
    const imgSrc = event.target.src
    const imageIndex = processedImages.value.findIndex(img => img.url === imgSrc)
    if (imageIndex !== -1) {
        imageLoadStates.value[imageIndex] = true
    }
    emit('media-loaded')
}

const handleModalImageLoad = () => {
    emit('media-loaded')
}

// Enhanced error handling
const handleImageError = (event) => {
    console.error('GeneratedMedia: Image failed to load:', {
        src: event.target.src,
        error: event,
        mediaItem: props.mediaItem,
        processedImages: processedImages.value
    })
    const target = event.target
    target.style.backgroundColor = '#f3f4f6'
    target.style.display = 'flex'
    target.style.alignItems = 'center'
    target.style.justifyContent = 'center'
    target.innerHTML = '<span style="color: #9ca3af; font-size: 14px;">Image unavailable</span>'
}

const handleVideoError = (event) => {
    console.warn('Video failed to load:', event.target.src)
}

const handleRetry = async () => {
    if (isRetrying.value) return

    isRetrying.value = true
    try {
        emit('retry', props.mediaItem.taskId)
        // Reset retry state after a delay
        setTimeout(() => {
            isRetrying.value = false
        }, 2000)
    } catch (error) {
        console.error('Retry failed:', error)
        isRetrying.value = false
    }
}

const getErrorMessage = (error) => {
    if (!error) return 'An unexpected error occurred during generation. Please try again.'

    // Common error messages with user-friendly alternatives
    const errorMap = {
        'timeout': 'Generation timed out. The server may be busy, please try again.',
        'rate_limit': 'Too many requests. Please wait a moment before trying again.',
        'invalid_prompt': 'The prompt contains invalid content. Please modify and try again.',
        'insufficient_credits': 'Insufficient credits to complete generation.',
        'server_error': 'Server error occurred. Please try again in a few moments.'
    }

    // Check if error matches any known patterns
    for (const [key, message] of Object.entries(errorMap)) {
        if (error.toLowerCase().includes(key)) {
            return message
        }
    }

    return error.length > 100 ? 'Generation failed due to a technical issue. Please try again.' : error
}

// Enhanced skeleton loading helpers
const getExpectedImageCount = () => {
    // Try to get expected count from media item metadata, otherwise default to 4
    return props.mediaItem.expectedCount || props.mediaItem.count || 5
}

const getExpectedVideoCount = () => {
    // Try to get expected count from media item metadata, otherwise default to 1
    return props.mediaItem.expectedCount || props.mediaItem.count || 1
}

const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-amber-100 text-amber-700',
        processing: 'bg-blue-100 text-blue-700',
        completed: 'bg-green-100 text-green-700',
        failed: 'bg-red-100 text-red-700',
        cancelled: 'bg-gray-100 text-gray-700'
    }
    return colors[status] || colors.pending
}

const getStatusIcon = (status) => {
    const iconMap = {
        pending: 'Clock',
        processing: 'Clock',
        completed: 'TickCircle',
        failed: 'CloseCircle',
        cancelled: 'MinusCircle'
    }
    return iconMap[status] || 'Clock'
}

const getProgressBarColor = (status) => {
    const colors = {
        pending: 'bg-amber-500',
        processing: 'bg-blue-500',
        completed: 'bg-green-500',
        failed: 'bg-red-500',
        cancelled: 'bg-gray-500'
    }
    return colors[status] || colors.pending
}

const getProgressWidth = (status) => {
    const widths = {
        pending: '25%',
        processing: '75%',
        completed: '100%',
        failed: '100%',
        cancelled: '50%'
    }
    return widths[status] || '0%'
}

const getProgressPercentage = (status) => {
    const percentages = {
        pending: 25,
        processing: 75,
        completed: 100,
        failed: 100,
        cancelled: 50
    }
    return percentages[status] || 0
}

const getStatusText = (status) => {
    const texts = {
        pending: 'Queued',
        processing: 'Generating',
        completed: 'Complete',
        failed: 'Failed',
        cancelled: 'Cancelled'
    }
    return texts[status] || 'Unknown'
}



const getVideoResults = (result) => {
    if (!result) return []

    const videos = []

    // Handle enhanced API response structure
    if (result.video_url) {
        videos.push({
            url: result.video_url,
            duration: result.duration || 'Unknown'
        })
    }

    if (result.video_urls && Array.isArray(result.video_urls)) {
        result.video_urls.forEach(url => {
            if (url && url !== result.video_url) { // Avoid duplicates
                videos.push({
                    url: url,
                    duration: 'Unknown'
                })
            }
        })
    }

    // Fallback to existing structure for backward compatibility
    if (videos.length === 0) {
        if (Array.isArray(result)) {
            return result.map(item => ({
                url: item.url || item,
                duration: item.duration || 'Unknown'
            }))
        }

        if (result.videos) {
            return result.videos.map(item => ({
                url: item.url || item,
                duration: item.duration || 'Unknown'
            }))
        }

        if (result.url) {
            return [{
                url: result.url,
                duration: result.duration || 'Unknown'
            }]
        }
    }

    return videos
}

// Reset image load states when processed images change
watch(processedImages, () => {
    imageLoadStates.value = {}
}, { immediate: true })
</script>

<style scoped>
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
}

/* Enhanced shimmer effect */
.animate-shimmer {
    background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
}

/* Modern Image Gallery Grid Layout */
.image-gallery-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    width: 100%;
}

/* Responsive grid columns */
@media (min-width: 640px) {
    .image-gallery-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }
}

@media (min-width: 768px) {
    .image-gallery-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
    }
}

@media (min-width: 1024px) {
    .image-gallery-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 16px;
    }
}

@media (min-width: 1280px) {
    .image-gallery-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 20px;
    }
}

/* Image tile styling */
.image-tile {
    position: relative;
    aspect-ratio: 1 / 1;
    overflow: hidden;
    border-radius: 8px;
    cursor: pointer;
    background-color: #f8fafc;
    transition: transform 0.2s ease-out;
}

.image-tile:hover {
    transform: scale(1.02);
}

.image-tile-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: opacity 0.2s ease-out;
}

.image-tile-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    transition: background 0.2s ease-out;
    pointer-events: none;
}

.image-tile:hover .image-tile-overlay {
    background: rgba(0, 0, 0, 0.1);
}

.image-tile:hover .image-tile-img {
    opacity: 0.95;
}

/* Smooth transitions for all interactive elements */
.transition-all {
    transition: all 0.2s ease-in-out;
}

/* Custom scrollbar for any overflow areas */
.overflow-x-auto::-webkit-scrollbar {
    height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Loading state for images */
.image-tile-img[src=""] {
    background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
}

/* Skeleton tile styling */
.skeleton-tile {
    position: relative;
    aspect-ratio: 1 / 1;
    overflow: hidden;
    border-radius: 8px;
    background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
}

.skeleton-content {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.skeleton-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #94a3b8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
