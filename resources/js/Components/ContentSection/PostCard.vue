<template>
    <div class="bg-white border rounded-lg p-16 hover:shadow-md transition-all duration-200 group relative"
         :class="{
             'border-[#FF5EAB] shadow-md': isEditing,
             'border-[#E5E7EB] hover:border-[#FF5EAB]/30': !isEditing
         }">
        <!-- Post Header -->
        <div class="flex items-center justify-between mb-12">
            <div class="flex items-center gap-8">
                <div class="flex items-center justify-center w-24 h-24 rounded-full"
                    :style="{ backgroundColor: platformColor + '20' }">
                    <VsxIcon :iconName="platformIcon" :size="14" :color="platformColor" type="linear" />
                </div>
                <div>
                    <h5 class="text-sm font-medium text-gray-700">{{ platformName }}</h5>
                </div>
            </div>

        </div>

        <!-- Post Content -->
        <div class="mb-16">
            <!-- Inline editing mode -->
            <div v-if="isEditing" class="space-y-12">
                <textarea
                    ref="editTextarea"
                    v-model="editingContent"
                    @keydown="handleEditKeyDown"
                    class="w-full px-12 py-8 bg-gray-50 rounded-lg text-sm outline-none border-0 resize-none min-h-[80px] leading-relaxed focus:bg-white"
                    placeholder="Edit your post content..."
                    rows="3"
                ></textarea>
                <div class="flex items-center gap-8">
                    <Button size="sm" color="action" @click="saveEdit">
                        Save
                    </Button>
                    <Button size="sm" variant="outline" @click="cancelEdit">
                        Cancel
                    </Button>
                </div>
            </div>

            <!-- Normal display mode -->
            <div v-else>
                <div
                    v-if="postHtmlContent"
                    class="prose prose-sm max-w-none text-gray-700 cursor-pointer hover:bg-gray-50 rounded p-8 transition-colors"
                    @click="startEdit"
                    v-html="postHtmlContent"
                ></div>
                <p
                    v-else
                    class="text-sm text-gray-700 cursor-pointer hover:bg-gray-50 rounded p-8 transition-colors leading-relaxed"
                    @click="startEdit"
                >
                    {{ postContent }}
                </p>
                <div class="text-xs text-gray-400 mt-4 opacity-60 group-hover:opacity-100 transition-opacity">
                    Click content or edit button to modify
                </div>
            </div>
        </div>

        <!-- Generated Media Display -->
        <div v-if="postMedia && postMedia.length > 0" class="mb-16 pt-12">
            <GeneratedMedia
                v-for="mediaItem in postMedia"
                :key="mediaItem.id"
                :mediaItem="mediaItem"
                @refresh="$emit('refresh-media', mediaItem.taskId)"
                @cancel="$emit('cancel-media', mediaItem.taskId)"
            />
        </div>

        <!-- Upload Messages -->
        <div v-if="uploadSuccess || uploadError" class="mb-16">
            <!-- Success Message -->
            <div v-if="uploadSuccess" class="flex items-center gap-8 px-12 py-8 bg-green-50 border border-green-200 rounded-lg text-green-700">
                <VsxIcon iconName="TickCircle" :size="16" type="linear" />
                <span class="text-sm">{{ uploadSuccess }}</span>
            </div>

            <!-- Error Message -->
            <div v-if="uploadError" class="flex items-center gap-8 px-12 py-8 bg-red-50 border border-red-200 rounded-lg text-red-700">
                <VsxIcon iconName="CloseCircle" :size="16" type="linear" />
                <span class="text-sm">{{ uploadError }}</span>
            </div>
        </div>

        <!-- Post Actions Footer -->
        <div class="flex items-center justify-end pt-12 border-t border-gray-100">
            <!-- Right side: Action buttons -->
            <div class="flex items-center gap-8">
                <!-- Edit Button -->
                <Button
                    @click="$emit('edit-post', postIndex)"
                    :disabled="isLoading"
                    class="flex items-center gap-4 px-8 py-4 text-xs font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Edit this post content"
                    :icon-left="EditIcon"
                >
                    <VsxIcon />
                    <span>Edit</span>
                </Button>

                <!-- Image Generation/Renewal -->
                <template v-if="hasCompletedMedia('image')">
                    <Button
                        @click="$emit('renew-image', postIndex)"
                        :disabled="isAnyMediaProcessActive"
                        class="flex items-center gap-4 px-8 py-4 text-xs font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Regenerate images for this post"
                        :icon-left="RefreshIcon"
                    >
                        <span>Renew Images</span>
                    </Button>
                </template>
                <template v-else>
                    <Button
                        @click="$emit('generate-image', postIndex)"
                        :disabled="isAnyMediaProcessActive"
                        class="flex items-center gap-4 px-8 py-4 text-xs font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Generate image for this post"
                        :icon-left="GalleryIcon"
                    >
                        <span>Generate Image</span>
                    </Button>
                </template>

                <!-- Video Generation/Renewal -->
                <template v-if="hasCompletedMedia('video')">
                    <Button
                        @click="$emit('renew-video', postIndex)"
                        :disabled="isAnyMediaProcessActive"
                        class="flex items-center gap-4 px-8 py-4 text-xs font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Regenerate videos for this post"
                    >
                        <span>Renew Videos</span>
                    </Button>
                </template>
                <template v-else>
                    <Button
                        :icon-left="RefreshIcon"
                        @click="$emit('generate-video', postIndex)"
                        :disabled="isAnyMediaProcessActive"
                        class="flex items-center gap-4 px-8 py-4 text-xs font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Generate video for this post"
                    >
                        <span>Generate Video</span>
                    </Button>
                </template>

                <!-- Add Media Button -->
                <Button
                    @click="triggerFileUpload"
                    :disabled="isUploading || isAnyMediaProcessActive"
                    :icon-left="isUploading ? LoadingIcon : DocumentUploadIcon"
                    size="sm"
                        class="flex items-center gap-4 px-8 py-4 text-xs font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    color="default"
                    title="Upload media from your device"
                >
                    {{ isUploading ? 'Uploading...' : 'Add Media' }}
                </Button>

                <!-- Hidden file input -->
                <input
                    ref="fileInput"
                    type="file"
                    @change="handleFileSelect"
                    accept="image/jpeg,image/jpg,image/png,image/gif,image/webp,video/mp4,video/mov,video/avi,video/webm"
                    class="hidden"
                    multiple="false"
                />
            </div>
        </div>

        <!-- Edited indicator -->
        <div v-if="isEdited" class="mt-8 text-xs text-gray-500 italic flex items-center gap-4">
            <VsxIcon iconName="Edit" :size="12" type="linear" />
            <span>Edited {{ editedAt }}</span>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, nextTick, h } from 'vue'
import { VsxIcon } from 'vue-iconsax'
import Button from '../Button/Button.vue'
import GeneratedMedia from '../MediaGeneration/GeneratedMedia.vue'
import Showdown from 'showdown'
import { useMediaUpload } from '../../composables/useMediaUpload.js'

// Icon components for Button props
const DocumentUploadIcon = () => h(VsxIcon, { iconName: 'DocumentUpload', size: 14, type: 'linear' })
const LoadingIcon = () => h(VsxIcon, { iconName: 'Loading', size: 14, type: 'linear', class: 'animate-spin' })
const  RefreshIcon = () => h(VsxIcon, { iconName: "Refresh", size: 14, type: 'linear'})
const GalleryIcon= () => h(VsxIcon, { iconName: "Gallery", size: 14, type: 'linear'})
const EditIcon = () => h(VsxIcon, { iconName:"Edit", size:"14", type:"linear"})
const props = defineProps({
    postContent: {
        type: String,
        required: true
    },
    postHtmlContent: {
        type: String,
        default: ''
    },
    postIndex: {
        type: Number,
        required: true
    },
    totalPosts: {
        type: Number,
        required: true
    },
    platformId: {
        type: String,
        required: true
    },
    platformName: {
        type: String,
        required: true
    },
    platformIcon: {
        type: String,
        required: true
    },
    platformColor: {
        type: String,
        required: true
    },
    postMedia: {
        type: Array,
        default: () => []
    },
    isLoading: {
        type: Boolean,
        default: false
    },
    isAnyMediaProcessActive: {
        type: Boolean,
        default: false
    },
    isEdited: {
        type: Boolean,
        default: false
    },
    editedAt: {
        type: String,
        default: ''
    }
})

const emit = defineEmits([
    'edit-post', 'duplicate-post', 'delete-post', 'update-post',
    'generate-image', 'generate-video', 'renew-image', 'renew-video',
    'refresh-media', 'cancel-media', 'media-uploaded'
])

// Local state
const isEditing = ref(false)
const editingContent = ref('')
const originalContent = ref('')
const editTextarea = ref(null)
const converter = new Showdown.Converter()

// File upload composable
const { isUploading, uploadError, uploadSuccess, uploadMedia } = useMediaUpload()
const fileInput = ref(null)

// Platform character limits
const platformLimits = {
    'twitter': 280,
    'instagram': 2200,
    'linkedin': 3000,
    'facebook': 63206,
    'pinterest': 500,
    'tiktok': 2200,
    'threads': 500,
    'bluesky': 300,
    'youtube': 5000,
    'blog': null // No limit
}

// Computed properties
const characterCount = computed(() => {
    return props.postContent ? props.postContent.length : 0
})

// Methods
const hasCompletedMedia = (mediaType) => {
    if (!props.postMedia || props.postMedia.length === 0) return false
    return props.postMedia.some(media =>
        media.type === mediaType &&
        media.status === 'completed' &&
        media.result
    )
}

const startEdit = () => {
    if (isEditing.value || props.isLoading) return

    isEditing.value = true
    editingContent.value = props.postContent || ''
    originalContent.value = props.postContent || ''

    nextTick(() => {
        if (editTextarea.value) {
            editTextarea.value.focus()
            editTextarea.value.setSelectionRange(editTextarea.value.value.length, editTextarea.value.value.length)
        }
    })
}

const saveEdit = () => {
    if (!editingContent.value.trim()) return

    emit('update-post', {
        index: props.postIndex,
        content: editingContent.value.trim(),
        htmlContent: converter.makeHtml(editingContent.value.trim())
    })

    cancelEdit()
}

const cancelEdit = () => {
    isEditing.value = false
    editingContent.value = ''
    originalContent.value = ''
}

const handleEditKeyDown = (event) => {
    if (event.key === 'Enter' && event.shiftKey) {
        event.preventDefault()
        saveEdit()
    } else if (event.key === 'Escape') {
        event.preventDefault()
        cancelEdit()
    }
}

// File upload methods
const triggerFileUpload = () => {
    if (isUploading.value || props.isAnyMediaProcessActive) return

    // Trigger file input click
    if (fileInput.value) {
        fileInput.value.click()
    }
}

const handleFileSelect = async (event) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
        // Use the composable to upload the file
        const mediaItem = await uploadMedia(file, props.postIndex)

        // Emit event to parent component with the uploaded media data
        emit('media-uploaded', {
            postIndex: props.postIndex,
            mediaData: mediaItem.data
        })
    } catch (error) {
        // Error handling is done by the composable
        console.error('Upload error:', error)
    }

    // Clear the file input
    if (fileInput.value) {
        fileInput.value.value = ''
    }
}
</script>
