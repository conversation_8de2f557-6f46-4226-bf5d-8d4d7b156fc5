import { ref } from 'vue'
import axios from 'axios'

export function useMediaUpload() {
    const isUploading = ref(false)
    const uploadError = ref(null)
    const uploadSuccess = ref(null)
    const uploadedMedia = ref([])

    const getHeaders = () => {
        const headers = {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken
        }

        return headers
    }

    const validateFile = (file) => {
        // Validate file type
        const allowedTypes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'video/mp4', 'video/mov', 'video/avi', 'video/webm'
        ]
        
        if (!allowedTypes.includes(file.type)) {
            throw new Error('Please select a valid image (JPG, PNG, GIF, WebP) or video (MP4, MOV, AVI, WebM) file.')
        }

        // Check file size (50MB limit)
        const maxSize = 50 * 1024 * 1024 // 50MB in bytes
        if (file.size > maxSize) {
            throw new Error('File size must be less than 50MB.')
        }

        return true
    }

    const uploadMedia = async (file, postIndex = null) => {
        isUploading.value = true
        uploadError.value = null
        uploadSuccess.value = null

        try {
            // Validate file
            validateFile(file)

            // Create FormData
            const formData = new FormData()
            formData.append('file', file)
            formData.append('fileName', file.name)

            // Make the API call
            const response = await axios.post('/api/social-media/media/upload', formData, {
                headers: getHeaders(),
                withCredentials: true
            })

            if (response.data.success) {
                const mediaItem = {
                    id: Date.now(),
                    type: file.type.startsWith('image/') ? 'image' : 'video',
                    fileName: file.name,
                    fileSize: file.size,
                    mimeType: file.type,
                    postIndex: postIndex,
                    uploadedAt: new Date().toISOString(),
                    data: response.data.data
                }

                uploadedMedia.value.push(mediaItem)
                uploadSuccess.value = 'Media uploaded successfully!'
                
                // Clear success message after 3 seconds
                setTimeout(() => {
                    uploadSuccess.value = null
                }, 3000)

                return mediaItem
            } else {
                throw new Error(response.data.error || 'Upload failed')
            }
        } catch (error) {
            let errorMessage = 'Failed to upload media. Please try again.'
            
            if (error.response?.data?.error) {
                errorMessage = error.response.data.error
            } else if (error.response?.data?.message) {
                errorMessage = error.response.data.message
            } else if (error.message) {
                errorMessage = error.message
            }
            
            uploadError.value = errorMessage
            
            // Clear error message after 5 seconds
            setTimeout(() => {
                uploadError.value = null
            }, 5000)
            
            throw error
        } finally {
            isUploading.value = false
        }
    }

    const clearError = () => {
        uploadError.value = null
    }

    const clearSuccess = () => {
        uploadSuccess.value = null
    }

    const clearMessages = () => {
        uploadError.value = null
        uploadSuccess.value = null
    }

    const getUploadedMediaByPostIndex = (postIndex) => {
        return uploadedMedia.value.filter(item => item.postIndex === postIndex)
    }

    const removeUploadedMedia = (mediaId) => {
        const index = uploadedMedia.value.findIndex(item => item.id === mediaId)
        if (index !== -1) {
            uploadedMedia.value.splice(index, 1)
        }
    }

    return {
        isUploading,
        uploadError,
        uploadSuccess,
        uploadedMedia,
        uploadMedia,
        clearError,
        clearSuccess,
        clearMessages,
        getUploadedMediaByPostIndex,
        removeUploadedMedia
    }
}
