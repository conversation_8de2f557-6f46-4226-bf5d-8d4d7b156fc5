import { ref } from 'vue'
import axios from 'axios'

export function useMediaUpload() {
    const isUploading = ref(false)
    const uploadError = ref(null)
    const uploadSuccess = ref(null)
    const uploadedMedia = ref([])
    const uploadProgress = ref(0)
    const uploadStatus = ref('idle') // 'idle', 'pending', 'processing', 'completed', 'failed'

    const getHeaders = () => {
        const headers = {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken
        }

        return headers
    }

    const validateFile = (file) => {
        // Validate file type
        const allowedTypes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'video/mp4', 'video/mov', 'video/avi', 'video/webm'
        ]

        if (!allowedTypes.includes(file.type)) {
            throw new Error('Please select a valid image (JPG, PNG, GIF, WebP) or video (MP4, MOV, AVI, WebM) file.')
        }

        // Check file size (50MB limit)
        const maxSize = 50 * 1024 * 1024 // 50MB in bytes
        if (file.size > maxSize) {
            throw new Error('File size must be less than 50MB.')
        }

        return true
    }

    const uploadMedia = async (file, postIndex = null) => {
        isUploading.value = true
        uploadError.value = null
        uploadSuccess.value = null
        uploadProgress.value = 0
        uploadStatus.value = 'pending'

        try {
            // Validate file
            validateFile(file)

            // Create FormData
            const formData = new FormData()
            formData.append('file', file)
            formData.append('fileName', file.name)

            // Update status to processing
            uploadStatus.value = 'processing'
            uploadProgress.value = 25

            // Make the API call with progress tracking
            const response = await axios.post('/api/social-media/media/upload', formData, {
                headers: getHeaders(),
                withCredentials: true,
                onUploadProgress: (progressEvent) => {
                    if (progressEvent.lengthComputable) {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                        uploadProgress.value = Math.min(percentCompleted, 90) // Cap at 90% until response
                    }
                }
            })

            if (response.data.success) {
                const apiData = response.data.data

                // Complete the progress
                uploadProgress.value = 100
                uploadStatus.value = 'completed'

                // Format the media item to match GeneratedMedia component expectations
                const mediaItem = {
                    id: apiData.id || Date.now(),
                    uuid: apiData.uuid,
                    type: apiData.is_image ? 'image' : 'video',
                    status: 'completed', // Uploaded media is immediately completed
                    fileName: apiData.name,
                    fileSize: apiData.size,
                    fileSizeMB: apiData.size_mb,
                    mimeType: apiData.mime,
                    isPrivate: apiData.is_private,
                    isImage: apiData.is_image,
                    isVideo: apiData.is_video,
                    url: apiData.url,
                    exists: apiData.exists,
                    postIndex: postIndex,
                    uploadedAt: apiData.created_at || new Date().toISOString(),
                    isUploaded: true, // Flag to distinguish from generated media
                    // Format result to match GeneratedMedia expectations
                    result: {
                        output: apiData.is_image ? {
                            image_url: apiData.url,
                            temporary_image_urls: [apiData.url]
                        } : {
                            video_url: apiData.url
                        }
                    },
                    // Original API response data
                    originalData: apiData
                }

                // Find and update the skeleton item, or add new item
                const skeletonIndex = uploadedMedia.value.findIndex(item =>
                    item.postIndex === postIndex && item.status === 'processing'
                )

                if (skeletonIndex !== -1) {
                    // Update the skeleton item with actual data
                    uploadedMedia.value[skeletonIndex] = mediaItem
                } else {
                    // Add new item if no skeleton found
                    uploadedMedia.value.push(mediaItem)
                }

                uploadSuccess.value = 'Media uploaded successfully!'

                // Clear success message after 3 seconds
                setTimeout(() => {
                    uploadSuccess.value = null
                }, 3000)

                return mediaItem
            } else {
                throw new Error(response.data.error || 'Upload failed')
            }
        } catch (error) {
            uploadStatus.value = 'failed'
            uploadProgress.value = 0

            let errorMessage = 'Failed to upload media. Please try again.'

            if (error.response?.data?.error) {
                errorMessage = error.response.data.error
            } else if (error.response?.data?.message) {
                errorMessage = error.response.data.message
            } else if (error.message) {
                errorMessage = error.message
            }

            uploadError.value = errorMessage

            // Update any skeleton items to failed status
            const skeletonIndex = uploadedMedia.value.findIndex(item =>
                item.postIndex === postIndex && item.status === 'processing'
            )

            if (skeletonIndex !== -1) {
                uploadedMedia.value[skeletonIndex].status = 'failed'
                uploadedMedia.value[skeletonIndex].error = errorMessage
            }

            // Clear error message after 5 seconds
            setTimeout(() => {
                uploadError.value = null
            }, 5000)

            throw error
        } finally {
            isUploading.value = false
        }
    }

    // Create skeleton media item for immediate display
    const createSkeletonMediaItem = (file, postIndex) => {
        const skeletonItem = {
            id: `skeleton-${Date.now()}`,
            uuid: `skeleton-${Date.now()}`,
            type: file.type.startsWith('image/') ? 'image' : 'video',
            status: 'processing',
            fileName: file.name,
            fileSize: file.size,
            fileSizeMB: (file.size / (1024 * 1024)).toFixed(2),
            mimeType: file.type,
            postIndex: postIndex,
            isUploaded: true,
            uploadedAt: new Date().toISOString(),
            // Skeleton doesn't have result yet
            result: null
        }

        uploadedMedia.value.push(skeletonItem)
        return skeletonItem
    }

    const clearError = () => {
        uploadError.value = null
    }

    const clearSuccess = () => {
        uploadSuccess.value = null
    }

    const clearMessages = () => {
        uploadError.value = null
        uploadSuccess.value = null
    }

    const getUploadedMediaByPostIndex = (postIndex) => {
        return uploadedMedia.value.filter(item => item.postIndex === postIndex)
    }

    const removeUploadedMedia = (mediaId) => {
        const index = uploadedMedia.value.findIndex(item => item.id === mediaId)
        if (index !== -1) {
            uploadedMedia.value.splice(index, 1)
        }
    }

    const clearUploadedMediaForPost = (postIndex) => {
        uploadedMedia.value = uploadedMedia.value.filter(item => item.postIndex !== postIndex)
    }

    const clearAllUploadedMedia = () => {
        uploadedMedia.value = []
    }

    return {
        isUploading,
        uploadError,
        uploadSuccess,
        uploadedMedia,
        uploadProgress,
        uploadStatus,
        uploadMedia,
        createSkeletonMediaItem,
        clearError,
        clearSuccess,
        clearMessages,
        getUploadedMediaByPostIndex,
        removeUploadedMedia,
        clearUploadedMediaForPost,
        clearAllUploadedMedia
    }
}
