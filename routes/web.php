<?php

use App\Http\Controllers\Admin\ReportController;
use App\Http\Controllers\Api\DataController;
use App\Http\Controllers\BuildController;
use App\Http\Controllers\CampaignsController;
use App\Http\Controllers\HubspotController;
use App\Http\Controllers\Inertia\DashboardController;
use App\Http\Controllers\Inertia\IndexController;
use App\Http\Controllers\Inertia\SetupController;
use App\Http\Controllers\Integrations\Auth\GoogleAuthController;
use App\Http\Controllers\Integrations\InsightsController;
use App\Http\Controllers\Integrations\SocialMediaController;
use App\Http\Controllers\UploadController;
use App\Models\User;
use App\Services\StripeApi;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/testmail', function () {
    // dd(auth()->user()->getCompany()->toArray());
    \Mail::to('<EMAIL>')->send(new \App\Mail\ContactUsMail(['email' => 'asd', 'name' => 'asd', 'company' => 'asdsa', 'message' => 'asdasdas', 'subject' => 'ASDS']));
});

Route::get('/testairbrake', function () {
    throw new \Exception(request()->message ?: 'Test vw');
});

Route::get('/testauth', function () {
    $user = User::find(1);
    dump($user->id);
    \Illuminate\Support\Facades\Auth::login($user);

    \Laravel\Fortify\Fortify::loginThrough(function () use ($user) {
        if ($user) {
            return $user;
        }
    });

    auth()->login($user, true);

    $userInstance = auth()->user();
    dump($userInstance);
    request()->session()->put([
        'password_hash' => $userInstance->getAuthPassword(),
        'password_hash_'.auth()->getDefaultDriver() => $userInstance->getAuthPassword(),
    ]);
});

Route::get('/testresetstatus', function () {
    $user = auth()->user();
    $user->refresh();
    dump($user->toArray());
    $stripeApi = new StripeApi;
    $hasActiveSubscription = $stripeApi->hasActiveSubscription($user->stripe_id ?: null, $user->email);
    dump('hasActiveSubscription', $hasActiveSubscription);
});

Route::controller(IndexController::class)
    ->as('inertia.index.')
    ->group(function () {

        Route::get('/', 'index')->name('index');
        Route::get('/faq', 'faqs')->name('faqs');
        Route::get('/select-subscription', 'select_subscription')->name('select_subscription');
        Route::get('/contact-us', 'contact_us')->name('contact_us');
        Route::get('/blog', 'blog')->name('blog');
        Route::get('/blog/{slug}', 'post_show')->name('post_show');

    });

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
    // \App\Http\Middleware\HasCompanyMiddleware::class,

])
->controller(DashboardController::class)
->group(function () {
    Route::get('/dashboard', 'index')->name('dashboard');
    Route::get('/benchmarker', 'benchmarker')->name('benchmarker');

        Route::get('/settings/integrations/facebook-data', 'facebook_data')->name('facebook_data');
        Route::get('/settings/integrations/instagram-data', 'instagram_data')->name('instagram_data');
    });

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
    \App\Http\Middleware\HasCompanyMiddleware::class,
])
    ->prefix('build')
    ->controller(BuildController::class)
    ->name('build.')
    ->group(function () {
        Route::get('/index', 'index')->name('index');

        Route::get('/posts', 'posts')->name('posts');
        Route::get('/posts/questions', 'postsQuestions')->name('posts.questions');
        Route::post('/posts/questions', 'savePostsQuestions')->name('posts.questions.save');
        Route::get('/posts/sources', 'postsSources')->name('posts.sources');
        Route::post('/posts/sources', 'savePostsSources')->name('posts.sources.save');
        Route::get('/posts/content', 'postsContent')->name('posts.content');
        Route::post('/posts/content', 'savePostsContent')->name('posts.content.save');

        Route::get('/calendar', 'calendar')->name('calendar');
        Route::post('/calendar', 'saveCalendar')->name('calendar.save');

        // Campaign routes - following RESTful conventions
        Route::get('/campaign', 'campaign')->name('campaigns.index');
        Route::post('/campaign', 'saveCampaign')->name('campaigns.store');
        Route::get('/campaign/platforms', 'campaignPlatforms')->name('campaigns.platforms');
        Route::post('/campaign/platforms', 'saveCampaignPlatforms')->name('campaigns.platforms.save');
        Route::get('/campaign/content', 'content')->name('campaigns.content');
        Route::post('/campaign/content', 'saveContent')->name('campaigns.content.save');

        Route::get('/audience', 'audience')->name('audience');
        Route::get('/audience-result/{uuid?}', 'audience_result')->name('audience.result');
        Route::post('/audience', 'saveAudience')->name('audience.save');
    });

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
    \App\Http\Middleware\HasCompanyMiddleware::class,
])
    ->controller(CampaignsController::class)
    ->group(function () {
        Route::get('/campaigns', 'index')->name('campaigns.index');
        Route::get('/campaigns/{uuid}', 'show')->name('campaigns.show');
        Route::post('/save-campaign', 'store')->name('save-campaign');
    });

Route::as('api.')
    ->prefix('api')
    ->group(function () {

        Route::controller(\App\Http\Controllers\Api\FeedbackController::class)
            ->group(function () {
                Route::post('/feedbacks/store', 'store')->name('store');
            });

        Route::controller(\App\Http\Controllers\WebhookController::class)
            ->group(function () {
                Route::get('/stripe', 'stripe')->name('stripe');
                Route::post('/stripe', 'stripe')->name('stripe');
            });

        Route::controller(\App\Http\Controllers\HomeController::class)
            ->group(function () {
                Route::post('/contact_us', 'contact_us')->name('contact_us');
            });

        Route::controller(\App\Http\Controllers\Account\RegisterController::class)
            ->group(function () {
                Route::post('/register', 'register')->name('register');
            });

        Route::as('setup.')
            ->middleware([
                'auth:sanctum',
                config('jetstream.auth_session'),
            ])
            ->prefix('setup')
            ->controller(\App\Http\Controllers\Account\SetupController::class)
            ->group(function () {
                Route::post('/company', 'company')->name('company');
                Route::post('/about', 'updateAbout')->name('about');
                Route::post('/add-users', 'add_users')->name('add_users');
                Route::post('/delete-users', 'delete_users')->name('delete_users');
                Route::post('/store-assets', 'store_assets')->name('store-assets');
            });

        Route::as('account.')
            ->middleware([
                'auth:sanctum',
                config('jetstream.auth_session'),
            ])
            ->prefix('account')
            ->controller(\App\Http\Controllers\Account\AccountController::class)
            ->group(function () {
                Route::get('/profiles', 'profiles')->name('profiles');
                Route::get('/plans', 'plans')->name('plans');
                Route::post('/refresh', 'refresh')->name('refresh');
            });

        Route::as('openai')
            ->middleware([
                'auth:sanctum',
                config('jetstream.auth_session'),
                \App\Http\Middleware\HasCompanyMiddleware::class,
            ])
            ->prefix('openai')
            ->controller(\App\Http\Controllers\OpenAIController::class)
            ->group(function () {
                Route::post('/chat', 'handleChat')->name('chat');
            });

        Route::prefix('social-media')
            ->controller(\App\Http\Controllers\Api\SocialMediaController::class)
            ->group(function () {
                Route::get('/social-connector/url', 'getSocialUrl')->name('social-connector.url');
                Route::post('/posts', 'publishPost')->name('posts.create');
                Route::get('/posts', 'getPosts')->name('posts.get');

                Route::post('/media/upload', 'uploadMedia')->name('media.upload');
                Route::post('/media/store-from-url', 'storeMediaFromUrl')->name('media.store-from-url');
                Route::get('/media/gallery', 'getMediaGallery')->name('media.gallery');
                Route::get('/media/{uuid}/metadata', 'getMediaMetadata')->name('media.metadata');
                Route::get('/media/{uuid}/url', 'getMediaUrl')->name('media.url');
                Route::delete('/media/{uuid}', 'deleteMedia')->name('media.delete');

                Route::get('/media/upload-url', 'getUploadUrl')->name('media.upload-url');
                Route::post('/media/resize', 'resizeMedia')->name('media.resize');
                Route::post('/media/verify', 'verifyMediaUrl')->name('media.verify');
            });

        // New streaming chat routes
        Route::as('chat.')
            ->middleware([
                'auth:sanctum',
                config('jetstream.auth_session'),
                \App\Http\Middleware\HasCompanyMiddleware::class,
            ])
            ->prefix('chat')
            ->controller(\App\Http\Controllers\ChatStreamController::class)
            ->group(function () {
                Route::post('/stream', 'stream')->name('stream');
                Route::post('/message', 'chat')->name('message');
            });

        // Quick Post Generation Routes
        Route::as('quick-post.')
            ->middleware([
                'auth:sanctum',
                config('jetstream.auth_session'),
                \App\Http\Middleware\HasCompanyMiddleware::class,
            ])
            ->prefix('quick-post/chat')
            ->controller(\App\Http\Controllers\QuickPostController::class)
            ->group(function () {
                Route::post('stream', 'stream')->name('stream');
            });

        // Media Generation Routes
        Route::as('media.')
            ->middleware([
                'auth:sanctum',
                config('jetstream.auth_session'),
                \App\Http\Middleware\HasCompanyMiddleware::class,
            ])
            ->prefix('media')
            ->controller(\App\Http\Controllers\MediaGenerationController::class)
            ->group(function () {
                Route::post('/image/generate', 'generateImage')->name('image.generate');
                Route::post('/video/generate', 'generateVideo')->name('video.generate');

                Route::get('/task/{taskId}/status', 'getTaskStatus')->name('task.status');
                Route::delete('/task/{taskId}/cancel', 'cancelTask')->name('task.cancel');

                Route::get('/models', 'getAvailableModels')->name('models');

                Route::post('/image/midjourney', 'generateMidjourneyImage')->name('image.midjourney');
                Route::post('/video/kling', 'generateKlingVideo')->name('video.kling');
            });

        Route::middleware(['auth'])->group(function () {
            Route::post('/uploads/filepond', [UploadController::class, 'process']);
            Route::delete('/uploads/revert', [UploadController::class, 'revert']);
        });

        Route::prefix('openai')
            ->controller(\App\Http\Controllers\OpenAIController::class)
            ->group(function () {
                Route::post('/chat', 'handleChat')->name('chat');
            });

    });

Route::as('settings.')
    ->middleware([
        'auth:sanctum',
        config('jetstream.auth_session'),
        \App\Http\Middleware\HasCompanyMiddleware::class,
    ])
    ->prefix('settings')
    ->group(function () {

        Route::as('profile.')
            ->prefix('profile')
            ->controller(\App\Http\Controllers\Settings\ProfileController::class)
            ->group(function () {
                Route::get('/', 'index')->name('index');
                Route::post('/update', 'update')->name('update');
                Route::post('/delete', 'delete')->name('delete');
                Route::post('/password', 'password')->name('password');
            });

        Route::as('notifications.')
            ->prefix('notifications')
            ->controller(\App\Http\Controllers\Settings\NotificationController::class)
            ->group(function () {
                Route::get('/', 'index')->name('index');
                Route::post('/update', 'update')->name('update');
            });

        Route::as('subscription.')
            ->prefix('subscription')
            ->controller(\App\Http\Controllers\Settings\SubscriptionController::class)
            ->group(function () {
                Route::get('/', 'index')->name('index');
            });

        Route::as('help.')
            ->controller(\App\Http\Controllers\Settings\HelpController::class)
            ->prefix('help')
            ->group(function () {
                Route::get('/', 'index')->name('index');
            });

        // Route::as('integrations.')
        //     ->controller(\App\Http\Controllers\Settings\IntegrationsController::class)
        //     ->prefix('integrations')
        //     ->group(function() {
        //         Route::get('/', 'index')->name('index');
        //     })
        // ;
    });
// Auth::routes();

// Route::get('/test', function() {
//     \Illuminate\Support\Facades\Password::sendResetLink(
//         ['email'=>'<EMAIL>']
//     );
// });

// Setup frontend routes
Route::as('setup.')
    ->middleware([
        'auth:sanctum',
        config('jetstream.auth_session'),
    ])
    ->prefix('setup')
    ->controller(SetupController::class)
    ->group(function () {
        Route::get('/organisation-name', 'organisation_name')->name('organisation-name');
        Route::get('/organisation-mission', 'organisation_mission')->name('organisation-mission');
        Route::get('/organisation-cause', 'organisation_cause')->name('organisation-cause');
        Route::get('/organisation-region', 'organisation_region')->name('organisation-region');
        Route::get('/organisation-language', 'organisation_language')->name('organisation-language');
        Route::get('/organisation-tone', 'organisation_tone')->name('organisation-tone');
        Route::get('/organisation-assets', 'organisation_assets')->name('organisation-assets');

        // Route::get('/about', 'company_about')->name('company-about');
        Route::redirect('/about', '/setup/organisation-name')->name('company-about');
        Route::get('/company-details', 'company_details')->name('company-details');
        Route::get('/about', 'company_about')->name('company-about');
        Route::get('/subscription', 'subscription')->name('subscription');
        Route::get('/add-users', 'add_users')->name('add-users');

        // Route::get('/connect-accounts', 'connect_accounts')->name('connect-accounts');

        Route::get('/thank-you', 'thank_you')->name('thank-you');
    });

Route::post('/subscribe', [HubspotController::class, 'subscribe'])->name('hubspot.subscribe');

Route::as('data.')
    /*->middleware([
        'auth:sanctum',
        config('jetstream.auth_session'),
    ])*/
    ->prefix('data')
    ->controller(DataController::class)
    ->group(function () {
        Route::get('/charity-categories', 'getCharityCategories');
        Route::post('/charity-sub-categories', 'getCharitySubCategories');
        Route::post('/charity-area-of-focus', 'getCharityAreaOfFocus');
        Route::post('/top-donor-details', 'getTopDonorDetails');
    });

Route::as('admin.')
    ->middleware(['auth', \App\Http\Middleware\IsAdminMiddleware::class])
    ->prefix('admin')
    ->controller(ReportController::class)
    ->group(function () {
        Route::as('reports.')
            ->prefix('reports')
            ->group(function () {
                Route::get('/feedbacks', 'feedbacks')->name('feedbacks');
                Route::get('/logins', 'logins')->name('logins');
                Route::get('/signups', 'signups')->name('signups');
                Route::get('/affinity-searches', 'affinitySearches')->name('affinity-searches');
            });
    });

Route::middleware(['auth'])
    ->group(function () {
        Route::get('/auth/meta', [SocialMediaController::class, 'redirectToMeta'])->name('auth.meta');
        Route::get('/auth/meta/callback', [SocialMediaController::class, 'handleMetaCallback'])->name('auth.meta.callback');

        Route::get('/auth/google', [GoogleAuthController::class, 'redirectToGoogle'])->name('auth.google');
        Route::get('/auth/google/callback', [GoogleAuthController::class, 'handleGoogleCallback'])->name('auth.google.callback');
        Route::post('/auth/google/select-property', [GoogleAuthController::class, 'storeSelectedProperty'])
            ->name('auth.google.select-property');

        Route::get('/auth/google/select-property-test', function () {
            Route::get('/auth/google/select-property-test', function () {
                $properties[] = [
                    'id' => '123',
                    'name' => 'DUMMY',
                    'id' => '123',
                    'name' => 'DUMMY',
                ];

                return view('integrations.google.select_property', [
                    'propertyList' => $properties,
                    'token' => $token ?? 'ASDSAD',
                    'token' => $token ?? 'ASDSAD',
                ]);
            });

            Route::get('/insights/facebook', [InsightsController::class, 'facebookInsights'])->name('insights.facebook');
            Route::get('/insights/instagram', [InsightsController::class, 'instagramInsights'])->name('insights.instagram');

            Route::post('/companies/integrations/delete', [SocialMediaController::class, 'delete'])->name('companies.integrations.delete');
        });
    });

Route::get('/auth/google/redirect', [GoogleAuthController::class, 'redirectToGoogleAuth'])->name('login.google');
Route::get('/auth/google/callback', [GoogleAuthController::class, 'handleGoogleAuthCallback']);
