<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use App\Services\StripeApi;
use Stripe;
use Stripe\SubscriptionSchedule;
use Stripe\Subscription;
use App\Models\User;
use App\Models\Company;

class SetupController extends Controller
{
    public function company(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string'],
            'legal_name' => ['nullable', 'string'],
            'address' => ['nullable', 'string'],
            'city' => ['nullable', 'string'],
            'region' => ['nullable', 'string'],
            'postcode' => ['nullable', 'string'],
            'country_id' => ['nullable', 'exists:countries,id'],
            'vat' => ['nullable', 'string'],
            'language_preference' => ['nullable', 'string'],
            'tone_preference' => ['nullable', 'string'],
            'logo' => ['nullable', 'image', 'max:2048', 'mimes:jpg,png,jpeg,gif,svg'],
            'marketing' => ['nullable', 'boolean']
        ]);

        $user = auth()->user();

        // Handle marketing consent for the user
        if ($request->has('marketing')) {
            $user->marketing = $request->boolean('marketing');
            $user->save();
        }

        $company = $user->company()->firstOrNew();
        $data = $request->toArray();

        // Remove marketing from company data since it belongs to the user
        unset($data['marketing']);

        if ($request->has('logo')) {
            $path = $request->file('logo')->store('logos', config('filesystems.default'));

            $data['logo'] = $path;
        }

        $company->forceFill($data);

        $company->save();

        return back()->with('flash', [
            'status' => 'success',
        ]);
    }

    public function updateAbout(Request $request)
    {
        $request->validate([
            'about' => ['nullable', 'string', 'max:600'],
            'language_preference' => ['nullable', 'string', 'max:255'],
            'tone_preference' => ['nullable', 'string', 'max:255'],
            'region' => ['nullable', 'string', 'max:255'],
            'general_category' => ['nullable','string','max:255'],
            'sub_category' => ['nullable','string','max:255'],
        ]);

        $company = auth()->user()->company;

        if (!$company) {
            return back()->with('flash', [
                'status' => 'fail',
                'message' => 'Company not found.',
            ]);
        }

        if ($request->about) {
            $company->about = $request->about;
        }

        if ($request->language_preference) {
            $company->language_preference = $request->language_preference;
        }

        if ($request->tone_preference) {
            $company->tone_preference = $request->tone_preference;
        }

        if ($request->region) {
            $company->region = $request->region;
        }

        if ($request->general_category) {
            $company->general_category = $request->general_category;
        }

        if ($request->sub_category) {
            $company->sub_category = $request->sub_category;
        }

        $company->save();

        return back()->with('flash', [
            'status' => 'success',
            'message' => 'About section updated successfully.',
        ]);
    }

    public function add_users(Request $request)
    {
        $user = auth()->user();

        //CHECK FOR ACTIVE SUBSCRIPTION BEFORE GOING TO THIS STEP
        // $stripeApi = new StripeApi;
        // $hasActiveSubscription = $stripeApi->hasActiveSubscription(null, $user->email);

        // if (!$hasActiveSubscription) {
        //     return back()->with('flash', [
        //         'status' => 'fail',
        //         'message' => "You cannot access this section without purchasing the subscription first."
        //     ]);
        // }

        $company = $user->company;

        $usersInCompany = User::where('company_id', $company->id)->where('user_type', User::TYPE_CONSUMER)->count();

        if ($usersInCompany >= 5) {
            return back()->with('flash', [
                'status' => 'fail',
                'message' => "You have reached the maximum limit of profiles in the company."
            ]);
        }

        $request->validate([
            'users.*.first_name' => ['required', 'string'],
            'users.*.last_name' => ['required', 'string'],
            'users.*.email' => ['required', 'string', 'unique:users,email'],
            'users.*.role_id' => ['required', 'in:'.implode(',', User::getRoleIds())],
        ], [
            'users.*.email' => 'The email has already been taken.'
        ]);


        foreach ($request->users as $key => $userData) {
            $createdUser = User::create([
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'email' => $userData['email'],
                'password' => bcrypt(time()),
                'role_id' => $userData['role_id'],
                'company_id' => $company->id,
                'user_type' => User::TYPE_CONSUMER,
                'stripe_id' => $user->stripe_id,
            ]);

            $subscriptionStatus = false;
            try {
                $stripe = new Stripe\StripeClient(config('services.stripe.secret'));
                Stripe\Stripe::setApiKey(config('services.stripe.secret'));

                $paymentMethod = $stripe->customers->allPaymentMethods(
                    $user->stripe_id,
                    ['type' => 'card']
                );

                $paymentId = $paymentMethod->data[0]->id ?? null;

                if ($paymentId) {

                    $stripeAPI = new StripeApi;

                    $createdUser->stripe_id = $user->stripe_id;
                    $subscription = Subscription::create([
                        'customer' => $user->stripe_id,
                        'items' => [['price' => config('services.stripe.extra_user_plan_id')]],
                        'default_payment_method' => $paymentId,
                        'payment_behavior' => 'allow_incomplete',
                        'expand' => ['latest_invoice.payment_intent'],
                    ]);

                    $createdUser->stripe_subscription_id = $subscription->id;
                    $createdUser->save();

                    $subscriptionStatus = true;
                }
            } catch (\Throwable $th) {
                throw $th;
            }
            try {
                \Mail::to($createdUser->email)->send(new \App\Mail\InviteTeamMail($user, $createdUser));
            } catch (\Exception $e) { }
        }

        return back()->with('flash', [
            'status' => 'success',
            'subscription_status' => $subscriptionStatus,
        ]);
    }

    //KEEP THIS EXPERIMENTAL METHOD PLEASE
    public function add_users2(Request $request)
    {
                /*$stripeAPI = new StripeApi;
            $createdUser = User::find(90);

        dd($createdUser->getHasStripeSubscriptionAttribute());*/
        //CHECK FOR ACTIVE SUBSCRIPTION BEFORE GOING TO THIS STEP
        // $stripeApi = new StripeApi;
        // $hasActiveSubscription = $stripeApi->hasActiveSubscription(null, $user->email);

        // if (!$hasActiveSubscription) {
        //     return back()->with('flash', [
        //         'status' => 'fail',
        //         'message' => "You cannot access this section without purchasing the subscription first."
        //     ]);
        // }
        //$user = auth()->user();
        $user = User::find(88);
        dump($user->toArray());
        /*$company = $user->company;

        $usersInCompany = User::where('company_id', $company->id)->where('user_type', User::TYPE_CONSUMER)->count();

        if ($usersInCompany >= 5) {
            return back()->with('flash', [
                'status' => 'fail',
                'message' => "You have reached the maximum limit of profiles in the company."
            ]);
        }

        $request->validate([
            'users.*.first_name' => ['required', 'string'],
            'users.*.last_name' => ['required', 'string'],
            'users.*.email' => ['required', 'string', 'unique:users,email'],
            'users.*.role_id' => ['required', 'in:'.implode(',', User::getRoleIds())],
        ], [
            'users.*.email' => 'The email has already been taken.'
        ]);*/


        //foreach ($request->users as $key => $userData) {
            /*$createdUser = User::create([
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'email' => $userData['email'],
                'password' => bcrypt(time()),
                'role_id' => $userData['role_id'],
                'company_id' => $company->id,
                'user_type' => User::TYPE_CONSUMER,
                //'stripe_id' => $user->stripe_id,
            ]);*/
            $createdUser = User::find(90);
            dump($createdUser->toArray());

            $stripe = new Stripe\StripeClient(config('services.stripe.secret'));
            Stripe\Stripe::setApiKey(config('services.stripe.secret'));

            $paymentMethod = $stripe->customers->allPaymentMethods(
                $user->stripe_id,
                ['type' => 'card']
            );

            $paymentId = $paymentMethod->data[0]->id ?? null;
            dump("paymentId", $paymentId);

            if ($paymentId) {

                $stripeAPI = new StripeApi;
                /*$stripeCustomer = $stripeAPI->getCustomer(null, [
                    'email' => $createdUser->email,
                    'name' => $createdUser->first_name,
                ]);*/
                //dump("stripeCustomer", $stripeCustomer);

                /*$attach = $stripe->paymentMethods->attach($paymentId, [
                    'customer' => $stripeCustomer->id,
                ]);

                dump("attach",$attach);*/

                $createdUser->stripe_id = $user->stripe_id;
                $subscription = Subscription::create([
                    'customer' => $user->stripe_id,
                    'items' => [['price' => config('services.stripe.extra_user_plan_id')]],
                    'default_payment_method' => $paymentId,
                    'payment_behavior' => 'allow_incomplete',
                    'expand' => ['latest_invoice.payment_intent'],
                ]);

                //$createdUser->update([''])
                $createdUser->stripe_subscription_id = $subscription->id;
                $createdUser->save();
                dump("subscription", $subscription);
            }

            try {
                //\Mail::to($createdUser->email)->send(new \App\Mail\InviteTeamMail($user, $createdUser));
            } catch (\Exception $e) {
                throw $e;
            }
        //}
        dd("ST");
        return back()->with('flash', [
            'status' => 'success',
        ]);
    }

    public function delete_users(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'ids.*' => ['required', 'exists:users,id'],
        ]);

        User::whereIn('id', $request->ids)->delete();

        return back()->with('flash', [
            'status' => 'success',
        ]);
    }

    public function store_assets(Request $request)
    {
        $data = $request->validate([
            'logo'       => 'nullable|string',
            'references' => 'nullable|array',
            'guidelines' => 'nullable|array',
        ]);

        $company = auth()->user()->company;

        // 1) Logo
        if (! empty($data['logo'])) {
            $company->update(['logo' => $data['logo']]);
        }

        // 2) References (wipes old ones)
        if (array_key_exists('references', $data)) {
            $company->references()->delete();
            if (count($data['references'])) {
                $refs = array_map(fn($path) => [
                    'path'  => $path,
                    'title' => basename($path),
                ], $data['references']);
                $company->references()->createMany($refs);
            }
        }

        // 3) Guidelines (wipes old ones)
        if (array_key_exists('guidelines', $data)) {
            $company->guidelines()->delete();
            if (count($data['guidelines'])) {
                $gls = array_map(fn($path) => [
                    'path'  => $path,
                    'title' => basename($path),
                ], $data['guidelines']);
                $company->guidelines()->createMany($gls);
            }
        }

        return back()->with('flash', ['status' => 'success']);
    }


}
